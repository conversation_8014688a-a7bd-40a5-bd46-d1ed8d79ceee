package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.QueryZonesRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType;
import com.ctrip.car.market.coupon.restful.contract.SeoZone;
import com.ctrip.car.market.coupon.restful.contract.seo.PoiDetail;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailResponseType;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Service
public class SeoPoiBusiness {

    private final ILog logger = LogManager.getLogger(SeoPoiBusiness.class);

    @Resource
    private SeoService service;

    @Resource
    private CarCrossRecommendedServiceProxy carCrossRecommendedServiceProxy;

    @Resource
    private SeoPlatformProxy seoPlatformProxy;

    private final static Set<Integer> hmtSet = Sets.newHashSet(53, 32, 33);

    public QuerySeoPoiDetailResponseType queryPoiDetail(QuerySeoPoiDetailRequestType request) {
        QuerySeoPoiDetailResponseType response = new QuerySeoPoiDetailResponseType();
        if (!checkRequest(request)) {
            Metrics.build().withTag("errorType", "paraError").withTag("result", "0").recordOne(MetricsEnum.SEO_POI.getTitle());
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        SeoHotDestinatioinfoDO hotDestinationInfo = service.queryHotDestinationFirst(request.getCountryId(), request.getCityId(), request.getPoiType(), request.getPoiCode());
        PlaceDetailsDTO globalInfo = service.queryCityDefaultPoi(request.getPoiCode(), request.getCityId(), request.getBaseRequest().getLocale());
        //优先使用qconfig配置
        if (globalInfo != null) {
            setPoi(request, response, globalInfo);
        } else if (hotDestinationInfo != null) {
            setPoi(request, response, hotDestinationInfo);
        } else {
            Metrics.build().withTag("errorType", "destinationError").withTag("result", "0").recordOne(MetricsEnum.SEO_POI.getTitle());
            response.setBaseResponse(ResponseUtil.fail("destination error"));
            return response;
        }
        Metrics.build().withTag("country", Optional.ofNullable(request.getCountryId()).orElse(0).toString())
                .withTag("city", Optional.ofNullable(request.getCityId()).orElse(0).toString())
                .withTag("result", Objects.nonNull(response.getPoiDetail()) ? "1" : "0")
                .recordOne(MetricsEnum.SEO_POI.getTitle());
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private void setPoi(QuerySeoPoiDetailRequestType request, QuerySeoPoiDetailResponseType response, SeoHotDestinatioinfoDO hotDestinationInfo) {
        QueryZonesRequestType zoneReq = new QueryZonesRequestType();
        zoneReq.setBaseRequest(request.getBaseRequest());
        zoneReq.setCityId(hotDestinationInfo.getCityId());
        zoneReq.setAirportCode(hotDestinationInfo.getPoiCode());
        QueryZonesResponseType zoneRes = carCrossRecommendedServiceProxy.queryZones(zoneReq);
        String locale = request.getBaseRequest().getLocale();
        if (zoneRes.getZone() != null) {
            SeoZone zone = zoneRes.getZone();
            String airportName = seoPlatformProxy.querySeoName(4, locale, hotDestinationInfo.getPoiCode());
            String countryName = seoPlatformProxy.querySeoName(1, locale, hotDestinationInfo.getCountryId().toString());
            String cityName = seoPlatformProxy.querySeoName(3, locale, hotDestinationInfo.getCityId().toString());
            response.setPoiDetail(new PoiDetail());
            response.getPoiDetail().setCountryId(hotDestinationInfo.getCountryId());
            response.getPoiDetail().setCountryName(StringUtils.isNotEmpty(countryName) ? countryName : service.getCountryName(hotDestinationInfo.getCountryId(), locale));
            response.getPoiDetail().setCityId(hotDestinationInfo.getCityId());
            response.getPoiDetail().setCityName(StringUtils.isNotEmpty(cityName) ? cityName : service.getCityName(hotDestinationInfo.getCityId(), locale));
            response.getPoiDetail().setPoiCode(zone.getAirportCode());
            response.getPoiDetail().setPoiName(StringUtils.isNotEmpty(airportName) ? airportName : zone.getZoneName());
            response.getPoiDetail().setLongitude(zone.getLongitude());
            response.getPoiDetail().setLatitude(zone.getLatitude());
            response.getPoiDetail().setPoiType(hotDestinationInfo.getPoiType());
            response.getPoiDetail().setIsHMT(hmtSet.contains(Optional.ofNullable(zone.getProvinceId()).orElse(0)));
            response.getPoiDetail().setCountryUrlName(service.queryUrlName(hotDestinationInfo.getCountryId().longValue(), 1));
            response.getPoiDetail().setCityUrlName(service.queryUrlName(hotDestinationInfo.getCityId().longValue(), 2));
            response.getPoiDetail().setPoiTitle(response.getPoiDetail().getPoiName());
        }
    }

    private void setPoi(QuerySeoPoiDetailRequestType request, QuerySeoPoiDetailResponseType response, PlaceDetailsDTO globalInfo) {
        response.setPoiDetail(new PoiDetail());
        response.getPoiDetail().setPoiCode(globalInfo.getCarPlaceId());
        response.getPoiDetail().setPoiName(globalInfo.getName());
        response.getPoiDetail().setLongitude(Optional.ofNullable(globalInfo.getLongitude()).orElse(BigDecimal.ZERO).doubleValue());
        response.getPoiDetail().setLatitude(Optional.ofNullable(globalInfo.getLatitude()).orElse(BigDecimal.ZERO).doubleValue());
        response.getPoiDetail().setCityId(request.getCityId());
        City city = service.getCity(request.getCityId(), request.getBaseRequest().getLocale());
        response.getPoiDetail().setCityName(city == null ? "" : city.getTranslationName());
        response.getPoiDetail().setCountryId(city == null ? 0 : city.getCountryId().intValue());
        response.getPoiDetail().setCountryName(service.getCountryName(city == null ? 0 : city.getCountryId().intValue(), request.getBaseRequest().getLocale()));
        response.getPoiDetail().setPoiType(NumberUtils.isNumber(globalInfo.getType()) ? Integer.parseInt(globalInfo.getType()) : 0);
        response.getPoiDetail().setIsHMT(false);
        if (city != null) {
            response.getPoiDetail().setIsHMT(hmtSet.contains(Optional.ofNullable(city.getProvinceId()).orElse(0L).intValue()));
            response.getPoiDetail().setCountryUrlName(service.queryUrlName(city.getCountryId(), 1));
            response.getPoiDetail().setCityUrlName(service.queryUrlName(city.getId(), 2));
        }
        response.getPoiDetail().setPoiTitle(response.getPoiDetail().getPoiName());
    }

    private boolean checkRequest(QuerySeoPoiDetailRequestType request) {
        if (request.getBaseRequest() == null) {
            return false;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) <= 0
                && Optional.ofNullable(request.getCountryId()).orElse(0) <= 0
                && StringUtils.isEmpty(request.getPoiCode())) {
            return false;
        }
        if (StringUtils.isEmpty(request.getBaseRequest().getRequestId())) {
            request.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        }
        return true;
    }

}
